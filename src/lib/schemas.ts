import { z } from "zod";

const instructionSchema = z.object({
  type: z.enum(["insert", "delete", "replace"]),
  position: z.string(),
  applied: z.boolean(),
});

const insertSchema = instructionSchema.extend({
  type: z.literal("insert"),
  text: z.string(),
});

const deleteSchema = instructionSchema.extend({
  type: z.literal("delete"),
  text: z.string(),
});

const replaceSchema = instructionSchema.extend({
  type: z.literal("replace"),
  before: z.string(),
  after: z.string(),
});

export const revisionSchema = z.object({
  instructions: z.array(insertSchema.or(deleteSchema).or(replaceSchema)),
});

// Export individual schemas if needed elsewhere
export { instructionSchema, insertSchema, deleteSchema, replaceSchema };
