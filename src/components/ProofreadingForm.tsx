"use client";

import FileUploader from "@/components/FileUploader";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, RotateCw, RotateCcw } from "lucide-react";
import Image from "next/image";

interface ProofreadingFormProps {
  title: string;
  files: FileList | null;
  images: string[];
  currentPage: number;
  isAnalyzing: boolean;
  isLoadingImages: boolean;
  onFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
  onPageChange: (page: number) => void;
  onRotateImage: (pageIndex: number, direction: "left" | "right") => void;
}

export default function ProofreadingForm({
  title,
  files,
  images,
  currentPage,
  isAnalyzing,
  isLoadingImages,
  onFileChange,
  onSubmit,
  onPageChange,
  onRotateImage,
}: ProofreadingFormProps) {
  return (
    <form onSubmit={onSubmit} className="flex flex-col gap-6 max-w-xl">
      <h2 className="text-xl font-bold">{title}</h2>
      <FileUploader files={files} id={title} handleFileChange={onFileChange} />

      {images.length > 0 && (
        <>
          <div className="flex items-center justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                if (currentPage > 0) {
                  onPageChange(currentPage - 1);
                }
              }}
              disabled={currentPage === 0}
            >
              <ChevronLeft className="h-4 w-4" />前
            </Button>
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => onRotateImage(currentPage, "left")}
                title="画像を左に90度回転"
              >
                <RotateCcw className="h-4 w-4" /> 左回転
              </Button>
              <span>
                {currentPage + 1} / {images.length}
              </span>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => onRotateImage(currentPage, "right")}
                title="画像を右に90度回転"
              >
                右回転
                <RotateCw className="h-4 w-4" />
              </Button>
            </div>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                if (currentPage < images.length - 1) {
                  onPageChange(currentPage + 1);
                }
              }}
              disabled={currentPage === images.length - 1}
            >
              次
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <Image
            src={images[currentPage]}
            alt={title}
            width={500}
            height={500}
            className="w-full max-w-xl border"
          />
        </>
      )}

      <Button
        type="submit"
        disabled={images.length === 0 || isAnalyzing || isLoadingImages}
        className="w-full max-w-xl"
      >
        {isAnalyzing
          ? "解析中..."
          : isLoadingImages
          ? "PDF変換中..."
          : "ファイルを解析"}
      </Button>
    </form>
  );
}
