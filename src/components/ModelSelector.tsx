"use client";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { MODELS } from "@/lib/const";

interface ModelSelectorProps {
  model: string;
  setModel: React.Dispatch<React.SetStateAction<string>>;
}

export default function ModelSelector({ model, setModel }: ModelSelectorProps) {
  return (
    <div className="w-full max-w-md">
      <Label htmlFor="content" className="mb-2">
        モデルを選択
      </Label>
      <Select value={model} onValueChange={setModel}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select a model" />
        </SelectTrigger>
        <SelectContent id="content">
          {Object.entries(MODELS).map(([provider, modelList]) => (
            <SelectGroup key={provider}>
              <SelectLabel>{provider}</SelectLabel>
              {modelList.map((m) => (
                <SelectItem key={m.id} value={m.id}>
                  {m.name}
                </SelectItem>
              ))}
            </SelectGroup>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
