"use client";

interface FileUploaderProps {
  files: FileList | null;
  id: string;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function FileUploader({
  files,
  id,
  handleFileChange,
}: FileUploaderProps) {
  // Determine if the selected files are PDF or images
  const isPdfSelected =
    files && files.length > 0 && files[0].type === "application/pdf";
  const areImagesSelected =
    files &&
    files.length > 0 &&
    ["image/jpeg", "image/png", "image/webp"].includes(files[0].type);

  return (
    <div className="w-full max-w-xl">
      <input
        type="file"
        accept=".pdf,image/png,image/jpeg,image/webp"
        multiple
        onChange={handleFileChange}
        className="hidden"
        id={id}
      />
      <label
        htmlFor={id}
        className="cursor-pointer block w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-all"
      >
        {files ? (
          <div>
            {isPdfSelected ? (
              <span className="text-blue-600">{files[0].name}</span>
            ) : areImagesSelected ? (
              <div className="space-y-2">
                <span className="text-blue-600">
                  {files.length} 枚の画像が選択されました
                </span>
                <div className="text-sm text-gray-500">
                  {Array.from(files).map((file, index) => (
                    <div key={index}>
                      {file.name} (
                      {file.type.replace("image/", "").toUpperCase()})
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <span className="text-red-600">
                対応していないファイル形式です
              </span>
            )}
          </div>
        ) : (
          <div>
            <div className="text-gray-600">
              クリックしてファイルをアップロード
            </div>
            <div className="text-sm text-gray-500 mt-1">
              PDFファイルまたは画像ファイル（JPEG、PNG、WEBP）
            </div>
          </div>
        )}
      </label>
    </div>
  );
}
