import { NextResponse } from "next/server";
import OpenAI from "openai";
import { zodTextFormat } from "openai/helpers/zod";
import { GoogleGenAI, Type } from "@google/genai";
import { z } from "zod";
import fs from "fs/promises";
import path from "path";
import type { Revision } from "@/types/revision";

const gemini = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Simple schema for checking if instructions are applied
const appliedCheckSchema = z.object({
  appliedStatus: z.array(z.boolean()),
});

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const model = formData.get("model") as string;
    const imageBefore = formData.get("imageBefore") as string;
    const imageAfter = formData.get("imageAfter") as string;
    const revisionsJson = formData.get("instructions") as string;

    // Validate required inputs
    if (!imageBefore || !imageAfter || !revisionsJson) {
      return NextResponse.json(
        { error: "imageBefore, imageAfter, and instructions are required" },
        { status: 400 }
      );
    }

    let revisions: Revision;
    try {
      revisions = JSON.parse(revisionsJson);
    } catch {
      return NextResponse.json(
        { error: "Invalid revisions JSON format" },
        { status: 400 }
      );
    }

    // Extract base64 data from data URLs
    const base64Before = imageBefore.replace(/^data:image\/[a-z]+;base64,/, "");
    const base64After = imageAfter.replace(/^data:image\/[a-z]+;base64,/, "");
    const imageBeforeBuffer = Buffer.from(base64Before, "base64").buffer;
    const imageAfterBuffer = Buffer.from(base64After, "base64").buffer;

    const instruction =
      "優秀な画像認識AIとして、修正前後の画像と修正指示を比較し、各修正指示が正しく適用されているかを確認してください。";
    const prompt = await fs.readFile(
      path.join(process.cwd(), "src/app/api/check-revisions/prompt.md"),
      "utf-8"
    );

    // Create detailed prompt with revision instructions
    const detailedPrompt = `${prompt}

## 確認対象の修正指示

${revisions.instructions
  .map(
    (instruction, index) => `
### 修正指示 ${index + 1}
- タイプ: ${instruction.type}
- 位置: ${instruction.position}
${
  instruction.type === "insert" && "text" in instruction
    ? `- 挿入テキスト: ${instruction.text}`
    : ""
}
${
  instruction.type === "delete" && "text" in instruction
    ? `- 削除テキスト: ${instruction.text}`
    : ""
}
${
  instruction.type === "replace" &&
  "before" in instruction &&
  "after" in instruction
    ? `- 置換前: ${instruction.before}\n- 置換後: ${instruction.after}`
    : ""
}
`
  )
  .join("")}

各修正指示が正しく適用されているかを確認し、修正指示の順番と同じ順序でtrue/falseの配列を返してください。`;

    let appliedStatus: boolean[];

    switch (model.split("-")[0]) {
      case "gemini": {
        const response = await gemini.models.generateContent({
          model: model,
          contents: [
            {
              inlineData: {
                mimeType: "image/jpeg" as const,
                data: Buffer.from(imageBeforeBuffer).toString("base64"),
              },
            },
            {
              inlineData: {
                mimeType: "image/jpeg" as const,
                data: Buffer.from(imageAfterBuffer).toString("base64"),
              },
            },
            { text: detailedPrompt },
          ],
          config: {
            temperature: 0.2,
            systemInstruction: instruction,
            responseMimeType: "application/json",
            responseSchema: {
              type: Type.OBJECT,
              properties: {
                appliedStatus: {
                  type: Type.ARRAY,
                  items: {
                    type: Type.BOOLEAN,
                    description:
                      "Whether the instruction was correctly applied",
                    nullable: false,
                  },
                  description:
                    "Array of boolean values indicating if each instruction was applied",
                },
              },
              required: ["appliedStatus"],
              propertyOrdering: ["appliedStatus"],
            },
          },
        });
        const result = JSON.parse(response.text ?? "{}") as {
          appliedStatus: boolean[];
        };
        appliedStatus = result.appliedStatus || [];
        break;
      }

      default: {
        // OpenAI
        const response = await openai.responses.parse({
          model: model,
          temperature: model.startsWith("gpt") ? 0.2 : null,
          instructions: instruction,
          input: [
            {
              role: "user",
              content: [
                { type: "input_text", text: detailedPrompt },
                {
                  type: "input_image" as const,
                  image_url: `data:image/jpeg;base64,${Buffer.from(
                    imageBeforeBuffer
                  ).toString("base64")}`,
                  detail: "auto" as const,
                },
                {
                  type: "input_image" as const,
                  image_url: `data:image/jpeg;base64,${Buffer.from(
                    imageAfterBuffer
                  ).toString("base64")}`,
                  detail: "auto" as const,
                },
              ],
            },
          ],
          text: {
            format: zodTextFormat(appliedCheckSchema, "result"),
          },
        });
        const result =
          response.output_parsed ??
          ({ appliedStatus: [] } as { appliedStatus: boolean[] });
        appliedStatus = result.appliedStatus || [];
        break;
      }
    }

    // Update the revision with applied status
    const updatedRevision: Revision = {
      ...revisions,
      instructions: revisions.instructions.map((instruction, index) => ({
        ...instruction,
        applied: appliedStatus[index] || false,
      })),
    };

    return NextResponse.json({ revision: updatedRevision });
  } catch (error) {
    console.error("Error processing files:", error);
    return NextResponse.json(
      { error: "Failed to process files" },
      { status: 500 }
    );
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
};
