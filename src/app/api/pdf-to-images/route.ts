import { NextResponse } from "next/server";
// import { pdf2img } from "@pdfme/converter";
import { pdf2img } from "@/lib/pdf2img";

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file || file.type !== "application/pdf") {
      return NextResponse.json(
        { error: "Only PDF files are allowed" },
        { status: 400 }
      );
    }

    const pdf = await file.arrayBuffer();
    const pdfImages = await pdf2img(pdf);

    // Convert ArrayBuffers to base64 data URLs
    const imageDataUrls = pdfImages.map((imageBuffer) => {
      const base64 = Buffer.from(imageBuffer).toString("base64");
      return `data:image/jpeg;base64,${base64}`;
    });

    return NextResponse.json({
      images: imageDataUrls,
      totalPages: pdfImages.length,
    });
  } catch (error) {
    console.error("Error converting PDF to images:", error);
    return NextResponse.json(
      { error: "Failed to convert PDF to images" },
      { status: 500 }
    );
  }
}
